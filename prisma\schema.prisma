// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model BoxType {
  id          Int      @id
  name        String
  code        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  Op Op[]
}

model BlisterType {
  id          Int      @id
  name        String
  code        String   @unique
  description String?
  slots       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  boxTypeId   Int
  limitPerBox Int

  Op Op[]
}

model ProductType {
  id          Int      @id
  name        String
  code        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  Op Op[]
}

model Op {
  id                Int       @id
  code              String
  boxTypeId         Int
  blisterTypeId     Int
  productTypeId     Int
  status            OpStatus  @default(PENDING)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  finishedAt        DateTime?
  quantityToProduce Int

  product ProductType @relation(fields: [productTypeId], references: [id])
  box     BoxType     @relation(fields: [boxTypeId], references: [id])
  blister BlisterType @relation(fields: [blisterTypeId], references: [id])

  OpBox OpBox[]
}

model OpBox {
  id                 String      @id @default(cuid())
  opId               Int
  code               String
  barCode            String?
  status             OpBoxStatus @default(PENDING)
  createdAt          DateTime    @default(now())
  barCodeGeneratedAt DateTime?
  packedAt           DateTime?
  breakAuthorizerId  Int?

  op              Op       @relation(fields: [opId], references: [id], onDelete: Cascade)
  breakAuthorizer Manager? @relation(fields: [breakAuthorizerId], references: [id])

  OpBoxBlister OpBoxBlister[]
}

model OpBoxBlister {
  id       String    @id @default(cuid())
  code     String
  opBoxId  String
  quantity Int       @default(0)
  packedAt DateTime?

  opBox OpBox @relation(fields: [opBoxId], references: [id], onDelete: Cascade)

  @@unique([opBoxId, code])
}

model Manager {
  id        Int      @id @default(autoincrement())
  firstName String
  lastName  String
  password  String
  email     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  OpBox OpBox[]
}

enum OpStatus {
  PENDING
  COMPLETED
}

enum OpBoxStatus {
  PENDING
  PACKAGED
  PACKAGED_W_BREAK
}
