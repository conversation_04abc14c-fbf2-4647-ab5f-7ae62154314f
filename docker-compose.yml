version: '3.8'

services:
  rabbitmq:
    image: rabbitmq:3-management
    container_name: gde_emb_rabbitmq
    ports:
      - "${RABBITMQ_EXTERNAL_PORT}:${RABBITMQ_PORT}"
      - "${RABBITMQ_MANAGEMENT_EXTERNAL_PORT}:${RABBITMQ_MANAGEMENT_PORT}"
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS}
    networks:
      - backend

  postgres:
    image: postgres:15
    container_name: gde_emb_postgres
    ports:
      - "${POSTGRES_EXTERNAL_PORT}:${POSTGRES_PORT}"
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    networks:
      - backend
    volumes:
      - pgdata:/var/lib/postgresql/data

networks:
  backend:

volumes:
  pgdata:
