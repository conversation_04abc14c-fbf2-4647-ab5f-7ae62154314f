{"name": "gde-insp-embalagem", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@prisma/client": "5.17.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@tanstack/react-table": "^8.19.3", "amqplib": "^0.10.4", "axios": "^1.7.9", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "fs": "^0.0.1-security", "lucide-react": "^0.417.0", "next": "14.2.5", "next-themes": "^0.3.0", "path": "^0.12.7", "pdf-lib": "^1.17.1", "puppeteer": "^23.6.1", "raw-body": "^3.0.0", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-jsbarcode": "^1.0.1", "react-to-print": "^3.0.2", "socket.io-client": "^4.7.5", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "winston": "^3.17.0", "zod": "^3.23.8"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/amqplib": "^0.10.5", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prisma": "^5.17.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}