import '@testing-library/jest-dom';

// Suprimir warnings específicos do React Testing Library
const originalError = console.error;

beforeAll(() => {
  console.error = (...args: any[]) => {
    // Suprimir warnings de act() que são comuns em testes de componentes React
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: An update to') && args[0].includes('inside a test was not wrapped in act'))
    ) {
      return;
    }
    
    // Suprimir warnings de punycode deprecation
    if (
      typeof args[0] === 'string' &&
      args[0].includes('DeprecationWarning: The `punycode` module is deprecated')
    ) {
      return;
    }
    
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Mock global para window.matchMedia (usado por alguns componentes)
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock global para ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock global para IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
